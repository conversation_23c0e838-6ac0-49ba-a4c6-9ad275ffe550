<script lang="ts" setup>
import {
  ArrowLeft,
  ArrowRight,
  Close,
  FullScreen,
  Loading,
  Menu,
  VideoPlay,
  ZoomIn,
  ZoomOut
} from "@element-plus/icons-vue"
import { ElMessage } from "element-plus"
import { onMounted, onUnmounted, ref, computed } from "vue"
import VideoStreamCanvas from "./components/VideoStreamCanvas.vue"
import { useVideoStream } from "./composables/useVideoStream"
import { useFrameManager } from "./composables/useFrameManager"
import { createFrameBlobUrl } from "./composables/useVideoStream"

// 响应式数据
const videoStreamCanvas = ref<InstanceType<typeof VideoStreamCanvas>>()
const currentVideo = ref("")
const videoList = ref<Array<{
  id: number
  name: string
  url: string
  duration: string
  size: string
  date: string
}>>([])
const currentIndex = ref(0)
const scale = ref(1)
const videoPosition = ref({ x: 0, y: 0 })
const showSidebar = ref(true)
const isLoading = ref(false)

// 视频流相关状态
const showGrid = ref(false)
const currentFps = ref(0)
const frameCount = ref(0)
const streamStats = ref({
  isConnected: false,
  totalFrames: 0,
  droppedFrames: 0,
  memoryUsage: 0
})

// 处理接收到的视频帧
const handleFrameReceived = async (frameData: ArrayBuffer) => {
  try {
    // 添加帧到缓存管理器
    const success = await frameManager.addFrame(frameData, 'jpeg')

    if (success) {
      // 获取当前帧并渲染到画布
      const currentFrame = frameManager.getCurrentFrame()
      if (currentFrame && videoStreamCanvas.value) {
        await videoStreamCanvas.value.processFrameData(frameData)
      }

      // 更新统计信息
      const stats = frameManager.getStats()
      currentFps.value = stats.currentFps
      frameCount.value = stats.totalFrames
      streamStats.value = {
        isConnected: videoStream.isConnected.value,
        totalFrames: stats.totalFrames,
        droppedFrames: stats.droppedFrames,
        memoryUsage: stats.memoryUsage
      }
    }
  } catch (error) {
    console.error('处理视频帧失败:', error)
  }
}

// 初始化视频流和帧管理器
const frameManager = useFrameManager({
  maxBufferSize: 30,
  targetFps: 30,
  enableFrameSkipping: true,
  memoryThreshold: 100,
  onFrameDropped: (frame) => {
    console.log('帧被丢弃:', frame.id)
  },
  onMemoryWarning: (usage) => {
    ElMessage.warning(`内存使用过高: ${usage.toFixed(1)}MB`)
  }
})

const videoStream = useVideoStream({
  url: "ws://*************:14580/ws/video",
  reconnectInterval: 3000,
  maxReconnectAttempts: 5,
  frameFormat: 'jpeg',
  onFrameReceived: handleFrameReceived,
  onError: (error) => {
    ElMessage.error(`视频流错误: ${error}`)
  },
  onConnected: () => {
    ElMessage.success('视频流连接成功')
  },
  onDisconnected: () => {
    ElMessage.info('视频流连接断开')
  }
})

// 模拟视频数据
const mockVideos = [
  {
    id: 1,
    name: "心脏超声检查.mp4",
    url: "/src/common/assets/video/3099938-hd_1920_1080_30fps.mp4",
    duration: "00:02:30",
    size: "15.2 MB",
    date: "2024-01-15"
  },
  {
    id: 2,
    name: "腹部CT扫描.mp4",
    url: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4",
    duration: "00:01:45",
    size: "8.5 MB",
    date: "2024-01-14"
  },
  {
    id: 3,
    name: "脑部MRI检查.mp4",
    url: "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_2mb.mp4",
    duration: "00:03:20",
    size: "12.8 MB",
    date: "2024-01-13"
  }
]

// 画布就绪回调
const handleCanvasReady = (canvas: any) => {
  console.log('视频流画布已就绪:', canvas)
}

// 缩放变化回调
const handleZoomChanged = (zoom: number) => {
  scale.value = zoom
}

// 初始化
onMounted(() => {
  videoList.value = mockVideos
  if (videoList.value.length > 0) {
    currentVideo.value = videoList.value[0].url
  }

  // 添加键盘事件监听
  document.addEventListener("keydown", handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener("keydown", handleKeydown)
  // 断开视频流连接
  videoStream.disconnect()
})

// 键盘事件处理
function handleKeydown(event: KeyboardEvent) {
  switch (event.key) {
    case "ArrowLeft":
      previousVideo()
      break
    case "ArrowRight":
      nextVideo()
      break
    case "+":
    case "=":
      zoomIn()
      break
    case "-":
      zoomOut()
      break
    case "Escape":
      resetView()
      break
  }
}

// WebSocket 连接函数
function connectWebSocket() {
  if (videoStream.isConnecting.value || videoStream.isConnected.value) {
    ElMessage.warning("视频流已连接或正在连接中")
    return
  }

  videoStream.connect()
}

// 断开WebSocket连接
function disconnectWebSocket() {
  videoStream.disconnect()
}

// 视频操作方法
function previousVideo() {
  if (currentIndex.value > 0) {
    currentIndex.value--
    currentVideo.value = videoList.value[currentIndex.value].url
    resetView()
  }
}

function nextVideo() {
  if (currentIndex.value < videoList.value.length - 1) {
    currentIndex.value++
    currentVideo.value = videoList.value[currentIndex.value].url
    resetView()
  }
}

function selectVideo(index: number) {
  currentIndex.value = index
  currentVideo.value = videoList.value[index].url
  resetView()
}

function toggleSidebar() {
  showSidebar.value = !showSidebar.value
}

// 视频播放控制函数已删除

function fullScreen() {
  if (videoStreamCanvas.value) {
    const canvasContainer = videoStreamCanvas.value.$el
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      canvasContainer.requestFullscreen()
    }
  }
}

// 缩放控制 - 委托给画布组件
function zoomIn() {
  videoStreamCanvas.value?.zoomIn()
}

function zoomOut() {
  videoStreamCanvas.value?.zoomOut()
}

function resetView() {
  videoStreamCanvas.value?.resetView()
}

// 切换网格显示
function toggleGrid() {
  showGrid.value = !showGrid.value
}

// 清空画布
function clearCanvas() {
  videoStreamCanvas.value?.clearCanvas()
  frameManager.clearBuffer()
  ElMessage.success('画布已清空')
}

// 视频样式计算已删除
</script>

<template>
  <div class="video-viewer">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <!-- 播放控制已删除 -->

        <!-- 视图控制 -->
        <el-button-group class="ml-2">
          <el-button :icon="ZoomOut" @click="zoomOut" title="缩小 (-)">
            缩小
          </el-button>
          <el-button @click="resetView" title="重置 (Esc)">
            {{ Math.round(scale * 100) }}%
          </el-button>
          <el-button :icon="ZoomIn" @click="zoomIn" title="放大 (+)">
            放大
          </el-button>
        </el-button-group>

        <!-- 音频控制已删除 -->

        <el-button :icon="FullScreen" @click="fullScreen" class="ml-2" title="全屏">
          全屏
        </el-button>

        <!-- 画布控制 -->
        <el-button-group class="ml-2">
          <el-button @click="toggleGrid" :type="showGrid ? 'primary' : 'default'" title="切换网格">
            网格
          </el-button>
          <el-button @click="clearCanvas" title="清空画布">
            清空
          </el-button>
        </el-button-group>
      </div>

      <div class="toolbar-right">
        <span class="video-info">
          {{ currentIndex + 1 }} / {{ videoList.length }}
        </span>

        <!-- WebSocket 连接控制 -->
        <el-button-group class="ml-2">
          <el-button
            v-if="!videoStream.isConnected.value"
            type="primary"
            :loading="videoStream.isConnecting.value"
            @click="connectWebSocket"
            title="连接WebSocket视频流"
          >
            {{ videoStream.isConnecting.value ? '连接中...' : '连接视频流' }}
          </el-button>
          <el-button
            v-else
            type="danger"
            @click="disconnectWebSocket"
            title="断开WebSocket连接"
          >
            断开连接
          </el-button>
        </el-button-group>

        <!-- 视频流状态信息 -->
        <div class="stream-info ml-2">
          <span v-if="videoStream.isConnected.value" class="fps-info">
            FPS: {{ currentFps }} | 帧数: {{ frameCount }}
          </span>
        </div>

        <!-- 侧边栏切换 -->
        <el-button
          :icon="showSidebar ? Close : Menu"
          @click="toggleSidebar"
          title="切换视频列表"
          class="ml-2"
        >
          {{ showSidebar ? '隐藏列表' : '显示列表' }}
        </el-button>
      </div>
    </div>

    <!-- 视频播放控制条已删除 -->

    <!-- 主内容区域 -->
    <div class="main-content" :class="{ 'with-sidebar': showSidebar }">
      <!-- 视频流画布显示区域 -->
      <VideoStreamCanvas
        ref="videoStreamCanvas"
        :show-grid="showGrid"
        :is-loading="isLoading"
        :websocket-url="'ws://*************:14580/ws/video'"
        :is-connected="videoStream.isConnected.value"
        :scale="scale"
        :position="videoPosition"
        @canvas-ready="handleCanvasReady"
        @zoom-changed="handleZoomChanged"
        @frame-received="(data) => console.log('Frame received:', data)"
        @error="(error) => ElMessage.error(error)"
      />

      <!-- 右侧边栏 -->
      <div v-if="showSidebar" class="sidebar">
        <div class="sidebar-header">
          <h3>视频列表</h3>
          <span class="video-count">{{ videoList.length }} 个视频</span>
        </div>
        <div class="video-list">
          <div
            v-for="(video, index) in videoList"
            :key="video.id"
            class="video-item"
            :class="{ active: index === currentIndex }"
            @click="selectVideo(index)"
          >
            <div class="video-thumbnail">
              <el-icon><VideoPlay /></el-icon>
            </div>
            <div class="video-info">
              <div class="video-name">
                {{ video.name }}
              </div>
              <div class="video-meta">
                <span class="duration">{{ video.duration }}</span>
                <span class="size">{{ video.size }}</span>
              </div>
              <div class="video-date">
                {{ video.date }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="navigation">
      <el-button
        class="nav-btn nav-prev"
        :disabled="currentIndex <= 0"
        @click="previousVideo"
        title="上一个视频 (←)"
      >
        <el-icon><ArrowLeft /></el-icon>
      </el-button>
      <el-button
        class="nav-btn nav-next"
        :disabled="currentIndex >= videoList.length - 1"
        @click="nextVideo"
        title="下一个视频 (→)"
      >
        <el-icon><ArrowRight /></el-icon>
      </el-button>
    </div>

    <!-- 快捷键提示 -->
    <div class="shortcuts-hint">
      <p>快捷键：← → 切换视频 | + - 缩放 | Esc 重置</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-viewer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1a1a1a;
  color: #fff;
  position: relative;
  overflow: hidden;

  .toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background-color: #2d2d2d;
    border-bottom: 1px solid #404040;
    z-index: 10;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .ml-2 {
        margin-left: 8px;
      }
    }

    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .video-info {
        font-size: 14px;
        color: #ccc;
      }

      .stream-info {
        .fps-info {
          font-size: 12px;
          color: #409eff;
          background: rgba(64, 158, 255, 0.1);
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }
  }

  // 视频控制条样式已删除

  .main-content {
    flex: 1;
    display: flex;
    overflow: hidden;

    &.with-sidebar {
      .video-container {
        flex: 1;
      }
    }
  }

  .sidebar {
    width: 320px;
    background-color: #2d2d2d;
    border-left: 1px solid #404040;
    display: flex;
    flex-direction: column;

    .sidebar-header {
      padding: 16px;
      border-bottom: 1px solid #404040;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
        color: #fff;
      }

      .video-count {
        font-size: 12px;
        color: #999;
      }
    }

    .video-list {
      flex: 1;
      overflow-y: auto;
      padding: 8px;

      .video-item {
        display: flex;
        padding: 12px;
        margin-bottom: 8px;
        background-color: #3a3a3a;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s ease;
        border: 2px solid transparent;

        &:hover {
          background-color: #404040;
        }

        &.active {
          background-color: #409eff;
          border-color: #66b1ff;

          .video-info {
            .video-name {
              color: #fff;
            }

            .video-meta,
            .video-date {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .video-thumbnail {
          width: 60px;
          height: 45px;
          background-color: #1a1a1a;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          flex-shrink: 0;

          .el-icon {
            font-size: 20px;
            color: #666;
          }
        }

        .video-info {
          flex: 1;
          min-width: 0;

          .video-name {
            font-size: 14px;
            font-weight: 500;
            color: #fff;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .video-meta {
            display: flex;
            gap: 8px;
            margin-bottom: 4px;

            span {
              font-size: 12px;
              color: #999;
            }

            .duration {
              &::before {
                content: "⏱ ";
              }
            }

            .size {
              &::before {
                content: "📁 ";
              }
            }
          }

          .video-date {
            font-size: 11px;
            color: #666;
          }
        }
      }
    }
  }

  .video-container {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background:
      radial-gradient(circle at 25% 25%, #333 1px, transparent 1px),
      radial-gradient(circle at 75% 75%, #333 1px, transparent 1px);
    background-size: 20px 20px;
    background-position:
      0 0,
      10px 10px;
    min-height: 0;

    .medical-video {
      max-width: none;
      max-height: none;
      transition: transform 0.1s ease-out;
      user-select: none;
      -webkit-user-drag: none;
    }

    .no-video {
      text-align: center;
      color: #666;
      font-size: 18px;
    }

    .loading-indicator {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: #ccc;

      .el-icon {
        font-size: 32px;
        margin-bottom: 8px;
      }
    }
  }

  .navigation {
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    transform: translateY(-50%);
    pointer-events: none;
    z-index: 5;

    .nav-btn {
      position: absolute;
      top: 0;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      font-size: 24px;
      font-weight: bold;
      pointer-events: auto;
      background-color: rgba(0, 0, 0, 0.6);
      border: 2px solid rgba(255, 255, 255, 0.3);
      color: #fff;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        background-color: rgba(0, 0, 0, 0.8);
        border-color: rgba(255, 255, 255, 0.6);
        transform: scale(1.1);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      &.nav-prev {
        left: 20px;
      }

      &.nav-next {
        right: 20px;
      }
    }
  }

  .shortcuts-hint {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.7);
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 12px;
    color: #ccc;
    z-index: 10;
    opacity: 0.8;
    transition: opacity 0.3s ease;

    &:hover {
      opacity: 1;
    }

    p {
      margin: 0;
    }
  }
}

// 全屏模式样式
:global(.video-viewer:fullscreen) {
  .shortcuts-hint {
    bottom: 40px;
  }
}
</style>
