import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'

export interface VideoStreamOptions {
  url: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  frameFormat?: 'jpeg' | 'png' | 'webp'
  onFrameReceived?: (frameData: A<PERSON>yBuffer) => void
  onError?: (error: string) => void
  onConnected?: () => void
  onDisconnected?: () => void
}

export interface VideoStreamStats {
  isConnected: boolean
  isConnecting: boolean
  reconnectAttempts: number
  totalFramesReceived: number
  bytesReceived: number
  connectionTime: number
  lastFrameTime: number
}

export function useVideoStream(options: VideoStreamOptions) {
  // WebSocket 实例
  let websocket: WebSocket | null = null
  let reconnectTimer: NodeJS.Timeout | null = null

  // 响应式状态
  const isConnected = ref(false)
  const isConnecting = ref(false)
  const stats = ref<VideoStreamStats>({
    isConnected: false,
    isConnecting: false,
    reconnectAttempts: 0,
    totalFramesReceived: 0,
    bytesReceived: 0,
    connectionTime: 0,
    lastFrameTime: 0
  })

  // 配置选项
  const config = {
    reconnectInterval: options.reconnectInterval || 3000,
    maxReconnectAttempts: options.maxReconnectAttempts || 5,
    frameFormat: options.frameFormat || 'jpeg'
  }

  // 连接 WebSocket
  const connect = async (): Promise<boolean> => {
    if (isConnecting.value || isConnected.value) {
      console.warn('WebSocket 已连接或正在连接中')
      return false
    }

    isConnecting.value = true
    stats.value.isConnecting = true

    try {
      websocket = new WebSocket(options.url)
      websocket.binaryType = 'arraybuffer' // 设置为接收二进制数据

      // 连接成功
      websocket.onopen = () => {
        isConnecting.value = false
        isConnected.value = true
        stats.value.isConnecting = false
        stats.value.isConnected = true
        stats.value.connectionTime = Date.now()
        stats.value.reconnectAttempts = 0

        ElMessage.success('视频流连接成功')
        console.log('WebSocket 视频流连接已建立:', options.url)
        
        options.onConnected?.()
      }

      // 接收消息（视频帧数据）
      websocket.onmessage = (event: MessageEvent) => {
        try {
          if (event.data instanceof ArrayBuffer) {
            // 更新统计信息
            stats.value.totalFramesReceived++
            stats.value.bytesReceived += event.data.byteLength
            stats.value.lastFrameTime = Date.now()

            // 处理帧数据
            options.onFrameReceived?.(event.data)
          } else {
            console.warn('收到非二进制数据:', typeof event.data)
          }
        } catch (error) {
          console.error('处理视频帧数据失败:', error)
          options.onError?.('处理视频帧数据失败')
        }
      }

      // 连接错误
      websocket.onerror = (error: Event) => {
        console.error('WebSocket 连接错误:', error)
        handleConnectionError('WebSocket 连接错误')
      }

      // 连接关闭
      websocket.onclose = (event: CloseEvent) => {
        console.log('WebSocket 连接已关闭:', event.code, event.reason)
        handleDisconnection()
      }

      return true

    } catch (error) {
      console.error('创建 WebSocket 连接失败:', error)
      handleConnectionError('创建 WebSocket 连接失败')
      return false
    }
  }

  // 断开连接
  const disconnect = () => {
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }

    if (websocket) {
      websocket.close()
      websocket = null
    }

    isConnecting.value = false
    isConnected.value = false
    stats.value.isConnecting = false
    stats.value.isConnected = false

    ElMessage.info('视频流连接已断开')
    options.onDisconnected?.()
  }

  // 处理连接错误
  const handleConnectionError = (errorMessage: string) => {
    isConnecting.value = false
    isConnected.value = false
    stats.value.isConnecting = false
    stats.value.isConnected = false

    ElMessage.error(errorMessage)
    options.onError?.(errorMessage)

    // 尝试重连
    attemptReconnect()
  }

  // 处理连接断开
  const handleDisconnection = () => {
    isConnecting.value = false
    isConnected.value = false
    stats.value.isConnecting = false
    stats.value.isConnected = false

    options.onDisconnected?.()

    // 尝试重连
    attemptReconnect()
  }

  // 尝试重连
  const attemptReconnect = () => {
    if (stats.value.reconnectAttempts >= config.maxReconnectAttempts) {
      ElMessage.error(`重连失败，已达到最大重连次数 (${config.maxReconnectAttempts})`)
      return
    }

    stats.value.reconnectAttempts++
    
    ElMessage.warning(`连接断开，${config.reconnectInterval / 1000}秒后尝试重连 (${stats.value.reconnectAttempts}/${config.maxReconnectAttempts})`)

    reconnectTimer = setTimeout(() => {
      console.log(`尝试重连 (${stats.value.reconnectAttempts}/${config.maxReconnectAttempts})`)
      connect()
    }, config.reconnectInterval)
  }

  // 发送控制命令（如果需要）
  const sendCommand = (command: any) => {
    if (!websocket || websocket.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket 未连接，无法发送命令')
      return false
    }

    try {
      websocket.send(JSON.stringify(command))
      return true
    } catch (error) {
      console.error('发送命令失败:', error)
      return false
    }
  }

  // 获取连接状态
  const getConnectionState = () => {
    if (!websocket) return 'CLOSED'
    
    switch (websocket.readyState) {
      case WebSocket.CONNECTING:
        return 'CONNECTING'
      case WebSocket.OPEN:
        return 'OPEN'
      case WebSocket.CLOSING:
        return 'CLOSING'
      case WebSocket.CLOSED:
        return 'CLOSED'
      default:
        return 'UNKNOWN'
    }
  }

  // 获取统计信息
  const getStats = () => ({
    ...stats.value,
    connectionState: getConnectionState(),
    averageBytesPerFrame: stats.value.totalFramesReceived > 0 
      ? Math.round(stats.value.bytesReceived / stats.value.totalFramesReceived)
      : 0,
    connectionDuration: stats.value.connectionTime > 0 
      ? Date.now() - stats.value.connectionTime
      : 0
  })

  // 重置统计信息
  const resetStats = () => {
    stats.value = {
      isConnected: isConnected.value,
      isConnecting: isConnecting.value,
      reconnectAttempts: 0,
      totalFramesReceived: 0,
      bytesReceived: 0,
      connectionTime: stats.value.connectionTime,
      lastFrameTime: 0
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    disconnect()
  })

  return {
    // 状态
    isConnected,
    isConnecting,
    stats,

    // 方法
    connect,
    disconnect,
    sendCommand,
    getConnectionState,
    getStats,
    resetStats
  }
}

// 视频流格式检测
export function detectVideoFrameFormat(arrayBuffer: ArrayBuffer): string {
  const uint8Array = new Uint8Array(arrayBuffer)
  
  // JPEG 文件头: FF D8 FF
  if (uint8Array.length >= 3 && 
      uint8Array[0] === 0xFF && 
      uint8Array[1] === 0xD8 && 
      uint8Array[2] === 0xFF) {
    return 'jpeg'
  }
  
  // PNG 文件头: 89 50 4E 47 0D 0A 1A 0A
  if (uint8Array.length >= 8 && 
      uint8Array[0] === 0x89 && 
      uint8Array[1] === 0x50 && 
      uint8Array[2] === 0x4E && 
      uint8Array[3] === 0x47) {
    return 'png'
  }
  
  // WebP 文件头: 52 49 46 46 ... 57 45 42 50
  if (uint8Array.length >= 12 && 
      uint8Array[0] === 0x52 && 
      uint8Array[1] === 0x49 && 
      uint8Array[2] === 0x46 && 
      uint8Array[3] === 0x46 &&
      uint8Array[8] === 0x57 && 
      uint8Array[9] === 0x45 && 
      uint8Array[10] === 0x42 && 
      uint8Array[11] === 0x50) {
    return 'webp'
  }
  
  return 'unknown'
}

// 创建 Blob URL
export function createFrameBlobUrl(arrayBuffer: ArrayBuffer, format?: string): string {
  const detectedFormat = format || detectVideoFrameFormat(arrayBuffer)
  let mimeType = 'image/jpeg' // 默认
  
  switch (detectedFormat) {
    case 'png':
      mimeType = 'image/png'
      break
    case 'webp':
      mimeType = 'image/webp'
      break
    case 'jpeg':
    default:
      mimeType = 'image/jpeg'
      break
  }
  
  const blob = new Blob([arrayBuffer], { type: mimeType })
  return URL.createObjectURL(blob)
}
