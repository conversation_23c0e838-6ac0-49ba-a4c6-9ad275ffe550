<template>
  <div class="performance-test">
    <h2>视频流性能测试</h2>
    
    <div class="test-controls">
      <el-button @click="startPerformanceTest" :disabled="isRunning" type="primary">
        开始性能测试
      </el-button>
      <el-button @click="stopTest" :disabled="!isRunning" type="danger">
        停止测试
      </el-button>
      <el-button @click="clearResults">
        清空结果
      </el-button>
    </div>

    <div class="test-results">
      <div class="metrics">
        <div class="metric-card">
          <h3>帧率性能</h3>
          <p>当前FPS: {{ currentFps }}</p>
          <p>平均FPS: {{ averageFps.toFixed(1) }}</p>
          <p>目标FPS: 30</p>
        </div>
        
        <div class="metric-card">
          <h3>渲染性能</h3>
          <p>平均渲染时间: {{ averageRenderTime.toFixed(2) }}ms</p>
          <p>最大渲染时间: {{ maxRenderTime.toFixed(2) }}ms</p>
          <p>渲染队列大小: {{ queueSize }}</p>
        </div>
        
        <div class="metric-card">
          <h3>内存使用</h3>
          <p>帧缓存数量: {{ bufferedFrames }}</p>
          <p>丢帧数量: {{ droppedFrames }}</p>
          <p>总处理帧数: {{ totalFrames }}</p>
        </div>
      </div>
    </div>

    <!-- 使用优化后的 VideoStreamCanvas 组件 -->
    <div class="canvas-container">
      <VideoStreamCanvas
        ref="videoCanvas"
        :show-grid="false"
        :is-loading="isLoading"
        @canvas-ready="handleCanvasReady"
        @frame-received="handleFrameReceived"
        @error="handleError"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import VideoStreamCanvas from './components/VideoStreamCanvas.vue'

// 测试状态
const isRunning = ref(false)
const isLoading = ref(false)

// 性能指标
const currentFps = ref(0)
const averageFps = ref(0)
const averageRenderTime = ref(0)
const maxRenderTime = ref(0)
const queueSize = ref(0)
const bufferedFrames = ref(0)
const droppedFrames = ref(0)
const totalFrames = ref(0)

// 组件引用
const videoCanvas = ref<InstanceType<typeof VideoStreamCanvas>>()

// 测试定时器和统计
let testTimer: NodeJS.Timeout | null = null
let fpsHistory: number[] = []
let renderTimeHistory: number[] = []
let testStartTime = 0

// 创建高质量测试图像数据
const createTestFrame = async (frameNumber: number): Promise<ArrayBuffer> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    canvas.width = 1920  // 高分辨率测试
    canvas.height = 1080
    const ctx = canvas.getContext('2d')!
    
    // 绘制复杂背景
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)
    gradient.addColorStop(0, `hsl(${(frameNumber * 5) % 360}, 70%, 50%)`)
    gradient.addColorStop(0.5, `hsl(${(frameNumber * 5 + 120) % 360}, 70%, 60%)`)
    gradient.addColorStop(1, `hsl(${(frameNumber * 5 + 240) % 360}, 70%, 40%)`)
    
    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // 绘制动态图案
    for (let i = 0; i < 50; i++) {
      ctx.fillStyle = `hsla(${(frameNumber * 10 + i * 7) % 360}, 80%, 60%, 0.6)`
      const x = (Math.sin(frameNumber * 0.1 + i) * 200 + canvas.width / 2)
      const y = (Math.cos(frameNumber * 0.1 + i) * 200 + canvas.height / 2)
      ctx.fillRect(x - 10, y - 10, 20, 20)
    }
    
    // 绘制帧信息
    ctx.fillStyle = 'white'
    ctx.font = 'bold 48px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(`Frame ${frameNumber}`, canvas.width / 2, canvas.height / 2)
    
    ctx.font = '24px Arial'
    ctx.fillText(`Time: ${new Date().toLocaleTimeString()}`, canvas.width / 2, canvas.height / 2 + 60)
    ctx.fillText(`Resolution: ${canvas.width}x${canvas.height}`, canvas.width / 2, canvas.height / 2 + 90)
    
    // 转换为高质量 JPEG
    canvas.toBlob((blob) => {
      if (blob) {
        blob.arrayBuffer().then(resolve)
      }
    }, 'image/jpeg', 0.9)
  })
}

// 开始性能测试
const startPerformanceTest = async () => {
  isRunning.value = true
  isLoading.value = true
  testStartTime = performance.now()
  
  // 重置统计
  fpsHistory = []
  renderTimeHistory = []
  totalFrames.value = 0
  droppedFrames.value = 0
  
  try {
    await new Promise(resolve => setTimeout(resolve, 500))
    isLoading.value = false
    
    // 开始高频率测试 (60 FPS)
    let frameCount = 0
    testTimer = setInterval(async () => {
      if (isRunning.value && videoCanvas.value) {
        frameCount++
        totalFrames.value = frameCount
        
        const frameStart = performance.now()
        
        // 创建并发送测试帧
        const frameData = await createTestFrame(frameCount)
        await videoCanvas.value.processFrameData(frameData)
        
        const frameEnd = performance.now()
        const renderTime = frameEnd - frameStart
        
        // 记录渲染时间
        renderTimeHistory.push(renderTime)
        if (renderTimeHistory.length > 100) {
          renderTimeHistory.shift() // 保持最近100帧的记录
        }
        
        // 更新性能指标
        updatePerformanceMetrics()
      }
    }, 1000 / 60) // 60 FPS 测试
    
    ElMessage.success('性能测试开始 - 60 FPS 高强度测试')
  } catch (error) {
    ElMessage.error('测试启动失败')
    stopTest()
  }
}

// 停止测试
const stopTest = () => {
  isRunning.value = false
  isLoading.value = false
  
  if (testTimer) {
    clearInterval(testTimer)
    testTimer = null
  }
  
  // 显示最终统计
  const testDuration = (performance.now() - testStartTime) / 1000
  ElMessage.info(`测试完成 - 运行时间: ${testDuration.toFixed(1)}秒`)
}

// 更新性能指标
const updatePerformanceMetrics = () => {
  // 计算平均渲染时间
  if (renderTimeHistory.length > 0) {
    averageRenderTime.value = renderTimeHistory.reduce((a, b) => a + b, 0) / renderTimeHistory.length
    maxRenderTime.value = Math.max(...renderTimeHistory)
  }
  
  // 获取画布性能统计
  if (videoCanvas.value) {
    const stats = videoCanvas.value.getPerformanceStats()
    currentFps.value = videoCanvas.value.getFps()
    queueSize.value = videoCanvas.value.getRenderQueueSize?.() || 0
    
    // 计算平均FPS
    fpsHistory.push(currentFps.value)
    if (fpsHistory.length > 30) {
      fpsHistory.shift()
    }
    averageFps.value = fpsHistory.reduce((a, b) => a + b, 0) / fpsHistory.length
    
    droppedFrames.value = stats.framesDropped
    bufferedFrames.value = stats.framesReceived
  }
}

// 清空结果
const clearResults = () => {
  currentFps.value = 0
  averageFps.value = 0
  averageRenderTime.value = 0
  maxRenderTime.value = 0
  queueSize.value = 0
  bufferedFrames.value = 0
  droppedFrames.value = 0
  totalFrames.value = 0
  fpsHistory = []
  renderTimeHistory = []
  
  videoCanvas.value?.clearCanvas()
}

// 事件处理
const handleCanvasReady = () => {
  ElMessage.success('画布初始化完成 - 准备开始性能测试')
}

const handleFrameReceived = (data: any) => {
  // 实时更新FPS
  currentFps.value = data.fps || 0
}

const handleError = (error: string) => {
  console.error('画布错误:', error)
  ElMessage.error(error)
}

// 组件卸载时清理
onUnmounted(() => {
  stopTest()
})
</script>

<style lang="scss" scoped>
.performance-test {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .test-results {
    margin-bottom: 20px;
    
    .metrics {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 16px;
      
      .metric-card {
        background: white;
        padding: 16px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        
        h3 {
          margin: 0 0 12px 0;
          color: #333;
          font-size: 16px;
        }
        
        p {
          margin: 6px 0;
          font-size: 14px;
          color: #666;
          
          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  .canvas-container {
    flex: 1;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}
</style>
