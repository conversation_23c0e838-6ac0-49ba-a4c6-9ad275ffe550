<template>
  <div class="test-video-stream">
    <h2>视频流测试页面</h2>
    
    <div class="test-controls">
      <el-button @click="startTest" :disabled="isRunning" type="primary">
        开始测试
      </el-button>
      <el-button @click="stopTest" :disabled="!isRunning" type="danger">
        停止测试
      </el-button>
      <el-button @click="sendTestFrame" :disabled="!isRunning">
        发送测试帧
      </el-button>
    </div>

    <div class="test-info">
      <p>连接状态: {{ connectionState }}</p>
      <p>已发送帧数: {{ sentFrames }}</p>
      <p>FPS: {{ currentFps }}</p>
      <p>内存使用: {{ memoryUsage.toFixed(2) }}MB</p>
    </div>

    <!-- 使用 VideoStreamCanvas 组件 -->
    <div class="canvas-container">
      <VideoStreamCanvas
        ref="videoCanvas"
        :show-grid="showGrid"
        :is-loading="isLoading"
        @canvas-ready="handleCanvasReady"
        @zoom-changed="handleZoomChanged"
        @frame-received="handleFrameReceived"
        @error="handleError"
      />
    </div>

    <div class="test-controls">
      <el-checkbox v-model="showGrid">显示网格</el-checkbox>
      <el-button @click="clearCanvas">清空画布</el-button>
      <el-button @click="resetView">重置视图</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import VideoStreamCanvas from './components/VideoStreamCanvas.vue'

// 测试状态
const isRunning = ref(false)
const isLoading = ref(false)
const showGrid = ref(false)
const sentFrames = ref(0)
const currentFps = ref(0)
const memoryUsage = ref(0)
const connectionState = ref('未连接')

// 组件引用
const videoCanvas = ref<InstanceType<typeof VideoStreamCanvas>>()

// 测试定时器
let testTimer: NodeJS.Timeout | null = null

// 创建测试图像数据
const createTestFrame = (frameNumber: number): ArrayBuffer => {
  // 创建一个简单的测试图像 (彩色方块)
  const canvas = document.createElement('canvas')
  canvas.width = 640
  canvas.height = 480
  const ctx = canvas.getContext('2d')!
  
  // 绘制背景
  ctx.fillStyle = `hsl(${(frameNumber * 10) % 360}, 50%, 50%)`
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 绘制帧号
  ctx.fillStyle = 'white'
  ctx.font = '48px Arial'
  ctx.textAlign = 'center'
  ctx.fillText(`Frame ${frameNumber}`, canvas.width / 2, canvas.height / 2)
  
  // 绘制时间戳
  ctx.font = '24px Arial'
  ctx.fillText(new Date().toLocaleTimeString(), canvas.width / 2, canvas.height / 2 + 60)
  
  // 转换为 JPEG 数据
  return new Promise<ArrayBuffer>((resolve) => {
    canvas.toBlob((blob) => {
      if (blob) {
        blob.arrayBuffer().then(resolve)
      }
    }, 'image/jpeg', 0.8)
  }) as any
}

// 开始测试
const startTest = async () => {
  isRunning.value = true
  isLoading.value = true
  connectionState.value = '连接中'
  
  try {
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    connectionState.value = '已连接'
    isLoading.value = false
    
    // 开始发送测试帧 (30 FPS)
    let frameCount = 0
    testTimer = setInterval(async () => {
      if (isRunning.value) {
        frameCount++
        sentFrames.value = frameCount
        
        // 创建并发送测试帧
        const frameData = await createTestFrame(frameCount)
        if (videoCanvas.value) {
          await videoCanvas.value.processFrameData(frameData)
        }
        
        // 更新 FPS (每秒计算一次)
        if (frameCount % 30 === 0) {
          currentFps.value = 30
        }
      }
    }, 1000 / 30) // 30 FPS
    
    ElMessage.success('测试开始')
  } catch (error) {
    ElMessage.error('测试启动失败')
    stopTest()
  }
}

// 停止测试
const stopTest = () => {
  isRunning.value = false
  isLoading.value = false
  connectionState.value = '已断开'
  
  if (testTimer) {
    clearInterval(testTimer)
    testTimer = null
  }
  
  ElMessage.info('测试停止')
}

// 发送单个测试帧
const sendTestFrame = async () => {
  if (!isRunning.value || !videoCanvas.value) return
  
  sentFrames.value++
  const frameData = await createTestFrame(sentFrames.value)
  await videoCanvas.value.processFrameData(frameData)
}

// 清空画布
const clearCanvas = () => {
  videoCanvas.value?.clearCanvas()
  sentFrames.value = 0
  currentFps.value = 0
  memoryUsage.value = 0
}

// 重置视图
const resetView = () => {
  videoCanvas.value?.resetView()
}

// 画布事件处理
const handleCanvasReady = (canvas: any) => {
  console.log('测试画布已就绪:', canvas)
  ElMessage.success('画布初始化完成')
}

const handleZoomChanged = (zoom: number) => {
  console.log('缩放变化:', zoom)
}

const handleFrameReceived = (data: any) => {
  console.log('帧接收事件:', data)
  currentFps.value = data.fps || 0
}

const handleError = (error: string) => {
  console.error('画布错误:', error)
  ElMessage.error(error)
}

// 组件卸载时清理
onUnmounted(() => {
  stopTest()
})
</script>

<style lang="scss" scoped>
.test-video-stream {
  padding: 20px;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  h2 {
    margin-bottom: 20px;
    color: #333;
  }

  .test-controls {
    margin-bottom: 20px;
    display: flex;
    gap: 12px;
    align-items: center;
  }

  .test-info {
    background: white;
    padding: 16px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    p {
      margin: 8px 0;
      font-size: 14px;
      color: #666;

      &:first-child {
        margin-top: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .canvas-container {
    flex: 1;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
  }
}
</style>
