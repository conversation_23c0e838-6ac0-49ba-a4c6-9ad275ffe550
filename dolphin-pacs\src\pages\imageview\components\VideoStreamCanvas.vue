<script lang="ts" setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed, defineExpose } from "vue"
import * as fabric from 'fabric'
import { ElMessage } from "element-plus"

defineOptions({
  name: "VideoStreamCanvas"
})

interface Props {
  showGrid?: boolean
  isLoading?: boolean
  websocketUrl?: string
  isConnected?: boolean
  scale?: number
  position?: { x: number, y: number }
}

const props = withDefaults(defineProps<Props>(), {
  showGrid: false,
  isLoading: false,
  websocketUrl: "",
  isConnected: false,
  scale: 1,
  position: () => ({ x: 0, y: 0 })
})

const emit = defineEmits<{
  canvasReady: [canvas: fabric.Canvas]
  zoomChanged: [zoom: number]
  frameReceived: [frameData: any]
  error: [error: string]
}>()

// Canvas相关引用
const canvasRef = ref<HTMLCanvasElement>()
const canvasContainer = ref<HTMLDivElement>()
let fabricCanvas: fabric.Canvas | null = null

// 画布状态
const canvasReady = ref(false)
const zoomLevel = ref(1)
const minZoom = 0.1
const maxZoom = 5

// 视频流状态
const currentFrame = ref<fabric.Image | null>(null)
const frameCount = ref(0)
const fps = ref(0)
const lastFrameTime = ref(0)
const frameBuffer = ref<ImageBitmap[]>([]) // 改为ImageBitmap缓存
const maxBufferSize = 30 // 最大缓存帧数

// 渲染队列管理
const renderQueue = ref<ImageBitmap[]>([])
const isRendering = ref(false)
const maxQueueSize = 5 // 最大渲染队列大小

// 性能监控
const performanceStats = ref({
  framesReceived: 0,
  framesDropped: 0,
  averageFps: 0,
  lastUpdateTime: Date.now(),
  renderTime: 0,
  queueSize: 0
})

// 画布配置
const canvasConfig = {
  backgroundColor: '#1a1a1a', // 深色背景适合视频显示
  selection: false, // 禁用选择，专注于视频显示
  preserveObjectStacking: true
}

// 网格配置
const gridConfig = {
  size: 20,
  color: '#333',
  strokeWidth: 1
}

// 动态计算画布尺寸
const getCanvasSize = () => {
  if (!canvasContainer.value) {
    return { width: 1000, height: 700 }
  }

  const container = canvasContainer.value
  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  if (containerWidth === 0 || containerHeight === 0) {
    return { width: 1000, height: 700 }
  }

  const padding = 20
  const width = Math.max(400, containerWidth - padding)
  const height = Math.max(300, containerHeight - padding)

  return { width, height }
}

// 等待容器尺寸就绪
const waitForContainerSize = async (maxRetries = 10): Promise<{ width: number, height: number }> => {
  for (let i = 0; i < maxRetries; i++) {
    const size = getCanvasSize()

    if (size.width > 400 && size.height > 300) {
      return size
    }

    await new Promise(resolve => setTimeout(resolve, 200))
  }

  return { width: 1000, height: 700 }
}

// 绘制网格
const drawGrid = () => {
  if (!fabricCanvas || !props.showGrid) {
    // 移除现有网格
    const objects = fabricCanvas?.getObjects().filter((obj: any) => obj.name === 'grid-line') || []
    objects.forEach((obj: any) => fabricCanvas!.remove(obj))
    fabricCanvas?.renderAll()
    return
  }

  // 移除现有网格
  const existingGridLines = fabricCanvas.getObjects().filter((obj: any) => obj.name === 'grid-line')
  existingGridLines.forEach((obj: any) => fabricCanvas!.remove(obj))

  const { width, height } = getCanvasSize()
  const { size, color, strokeWidth } = gridConfig

  // 绘制垂直线
  for (let i = 0; i <= width; i += size) {
    const line = new (fabric as any).Line([i, 0, i, height], {
      stroke: color,
      strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line'
    })
    fabricCanvas.add(line)
  }

  // 绘制水平线
  for (let i = 0; i <= height; i += size) {
    const line = new (fabric as any).Line([0, i, width, i], {
      stroke: color,
      strokeWidth,
      selectable: false,
      evented: false,
      name: 'grid-line'
    })
    fabricCanvas.add(line)
  }

  fabricCanvas.renderAll()
}

// 更新网格
const updateGrid = () => {
  drawGrid()
}

// 添加事件监听
const addEventListeners = () => {
  if (!fabricCanvas) return
  
  // 鼠标滚轮缩放
  fabricCanvas.on('mouse:wheel', (opt) => {
    const delta = opt.e.deltaY
    let zoom = fabricCanvas!.getZoom()
    zoom *= 0.999 ** delta

    if (zoom > maxZoom) zoom = maxZoom
    if (zoom < minZoom) zoom = minZoom

    fabricCanvas!.zoomToPoint(new (fabric as any).Point(opt.e.offsetX, opt.e.offsetY), zoom)
    zoomLevel.value = Math.round(zoom * 100) / 100
    emit('zoomChanged', zoomLevel.value)

    updateGrid()

    opt.e.preventDefault()
    opt.e.stopPropagation()
  })

  // 鼠标拖拽平移
  let isDragging = false
  let lastPosX = 0
  let lastPosY = 0

  fabricCanvas.on('mouse:down', (opt) => {
    const evt = opt.e
    isDragging = true
    fabricCanvas!.selection = false
    lastPosX = evt.clientX
    lastPosY = evt.clientY
  })

  fabricCanvas.on('mouse:move', (opt) => {
    if (isDragging) {
      const evt = opt.e
      const vpt = fabricCanvas!.viewportTransform!
      vpt[4] += evt.clientX - lastPosX
      vpt[5] += evt.clientY - lastPosY
      fabricCanvas!.requestRenderAll()
      lastPosX = evt.clientX
      lastPosY = evt.clientY
    }
  })

  fabricCanvas.on('mouse:up', () => {
    isDragging = false
    fabricCanvas!.selection = true
  })
}

// 处理视频帧数据 - 优化版本
const processFrameData = async (frameData: ArrayBuffer | Blob) => {
  try {
    const startTime = performance.now()

    // 检查渲染队列是否过载
    if (renderQueue.value.length >= maxQueueSize) {
      // 丢弃最旧的帧
      const droppedFrame = renderQueue.value.shift()
      if (droppedFrame) {
        droppedFrame.close()
        performanceStats.value.framesDropped++
      }
    }

    let imageBitmap: ImageBitmap

    if (frameData instanceof ArrayBuffer) {
      // 使用 createImageBitmap 替代 URL.createObjectURL
      const blob = new Blob([frameData], { type: 'image/jpeg' })
      imageBitmap = await createImageBitmap(blob)
    } else {
      // 处理 Blob
      imageBitmap = await createImageBitmap(frameData)
    }

    // 添加到渲染队列
    renderQueue.value.push(imageBitmap)

    // 管理帧缓存
    frameBuffer.value.push(imageBitmap)
    if (frameBuffer.value.length > maxBufferSize) {
      const oldFrame = frameBuffer.value.shift()
      if (oldFrame) {
        oldFrame.close() // 释放 ImageBitmap 内存
      }
    }

    // 异步渲染，避免阻塞
    scheduleRender()

    // 更新性能统计
    const processingTime = performance.now() - startTime
    updatePerformanceStats(processingTime)

    emit('frameReceived', { frameCount: frameCount.value, fps: fps.value })

  } catch (error) {
    console.error('处理视频帧失败:', error)
    emit('error', '处理视频帧失败')
  }
}

// 渲染调度器 - 避免渲染阻塞
const scheduleRender = () => {
  if (isRendering.value || renderQueue.value.length === 0) {
    return
  }

  // 使用 requestAnimationFrame 确保在下一帧渲染
  requestAnimationFrame(async () => {
    await processRenderQueue()
  })
}

// 处理渲染队列
const processRenderQueue = async () => {
  if (isRendering.value || !fabricCanvas || renderQueue.value.length === 0) {
    return
  }

  isRendering.value = true
  performanceStats.value.queueSize = renderQueue.value.length

  try {
    // 取最新的帧进行渲染，丢弃中间帧以保持实时性
    const latestFrame = renderQueue.value.pop()

    // 清空队列并释放其他帧
    while (renderQueue.value.length > 0) {
      const droppedFrame = renderQueue.value.shift()
      if (droppedFrame) {
        droppedFrame.close()
        performanceStats.value.framesDropped++
      }
    }

    if (latestFrame) {
      await renderFrameOptimized(latestFrame)
      latestFrame.close() // 渲染完成后释放
    }

  } catch (error) {
    console.error('渲染队列处理失败:', error)
  } finally {
    isRendering.value = false

    // 如果队列中还有帧，继续处理
    if (renderQueue.value.length > 0) {
      scheduleRender()
    }
  }
}

// 优化的渲染函数 - 复用Fabric对象
const renderFrameOptimized = async (imageBitmap: ImageBitmap): Promise<void> => {
  if (!fabricCanvas) {
    throw new Error('画布未初始化')
  }

  const renderStart = performance.now()

  try {
    // 如果是第一帧或需要重新创建对象
    if (!currentFrame.value) {
      await createNewFrameObject(imageBitmap)
    } else {
      // 复用现有对象，只更新图像内容
      await updateFrameContent(imageBitmap)
    }

    frameCount.value++
    performanceStats.value.renderTime = performance.now() - renderStart

  } catch (error) {
    console.error('渲染帧失败:', error)
    // 如果更新失败，尝试重新创建
    await createNewFrameObject(imageBitmap)
  }
}

// 创建新的帧对象
const createNewFrameObject = async (imageBitmap: ImageBitmap): Promise<void> => {
  return new Promise((resolve, reject) => {
    try {
      // 创建临时canvas来转换ImageBitmap
      const tempCanvas = document.createElement('canvas')
      tempCanvas.width = imageBitmap.width
      tempCanvas.height = imageBitmap.height
      const tempCtx = tempCanvas.getContext('2d')

      if (!tempCtx) {
        reject(new Error('无法获取canvas上下文'))
        return
      }

      tempCtx.drawImage(imageBitmap, 0, 0)
      const dataURL = tempCanvas.toDataURL('image/jpeg', 0.8)

      (fabric as any).Image.fromURL(dataURL, (img: fabric.Image) => {
        try {
          // 移除旧帧
          if (currentFrame.value) {
            fabricCanvas!.remove(currentFrame.value)
          }

          // 设置图像属性
          img.set({
            left: 0,
            top: 0,
            selectable: false,
            evented: false,
            name: 'video-frame'
          })

          // 计算并应用缩放
          const canvasWidth = fabricCanvas!.getWidth()
          const canvasHeight = fabricCanvas!.getHeight()
          const scale = Math.min(canvasWidth / img.width!, canvasHeight / img.height!)

          img.scale(scale)
          img.center()

          // 添加到画布
          fabricCanvas!.add(img)
          fabricCanvas!.sendToBack(img)
          fabricCanvas!.renderAll()

          currentFrame.value = img
          resolve()
        } catch (error) {
          reject(error)
        }
      }, { crossOrigin: 'anonymous' })
    } catch (error) {
      reject(error)
    }
  })
}

// 更新帧内容 - 复用对象
const updateFrameContent = async (imageBitmap: ImageBitmap): Promise<void> => {
  if (!currentFrame.value) return

  try {
    // 创建临时canvas来转换ImageBitmap
    const tempCanvas = document.createElement('canvas')
    tempCanvas.width = imageBitmap.width
    tempCanvas.height = imageBitmap.height
    const tempCtx = tempCanvas.getContext('2d')

    if (!tempCtx) {
      throw new Error('无法获取canvas上下文')
    }

    tempCtx.drawImage(imageBitmap, 0, 0)
    const dataURL = tempCanvas.toDataURL('image/jpeg', 0.8)

    // 直接更新图像源，避免重新创建对象
    const img = currentFrame.value as any
    img.setSrc(dataURL, () => {
      fabricCanvas!.renderAll()
    })
  } catch (error) {
    console.error('更新帧内容失败:', error)
    // 如果更新失败，回退到创建新对象
    await createNewFrameObject(imageBitmap)
  }
}

// 更新性能统计 - 优化版本
const updatePerformanceStats = (processingTime?: number) => {
  const now = Date.now()
  const timeDiff = now - performanceStats.value.lastUpdateTime

  if (timeDiff >= 1000) { // 每秒更新一次
    fps.value = Math.round((frameCount.value - performanceStats.value.framesReceived) * 1000 / timeDiff)
    performanceStats.value.framesReceived = frameCount.value
    performanceStats.value.lastUpdateTime = now
    performanceStats.value.averageFps = fps.value

    if (processingTime !== undefined) {
      performanceStats.value.renderTime = processingTime
    }
  }
}

// 更新画布尺寸
const updateCanvasSize = () => {
  if (!fabricCanvas || !canvasContainer.value) return

  const { width, height } = getCanvasSize()
  const currentWidth = fabricCanvas.getWidth()
  const currentHeight = fabricCanvas.getHeight()

  if (Math.abs(currentWidth - width) > 5 || Math.abs(currentHeight - height) > 5) {
    fabricCanvas.setDimensions({ width, height })
    updateGrid()
    fabricCanvas.renderAll()
  }
}

// 窗口大小变化处理
const handleResize = () => {
  nextTick(() => {
    updateCanvasSize()
  })
}

// 清空画布 - 优化版本
const clearCanvas = () => {
  if (!fabricCanvas) return

  // 清空帧缓存 - 释放ImageBitmap
  frameBuffer.value.forEach((imageBitmap: ImageBitmap) => imageBitmap.close())
  frameBuffer.value = []

  // 清空渲染队列
  renderQueue.value.forEach((imageBitmap: ImageBitmap) => imageBitmap.close())
  renderQueue.value = []

  // 移除所有对象（除了网格）
  const objects = fabricCanvas.getObjects().filter((obj: any) => obj.name !== 'grid-line')
  objects.forEach((obj: any) => fabricCanvas!.remove(obj))

  currentFrame.value = null
  frameCount.value = 0
  fps.value = 0
  isRendering.value = false

  fabricCanvas.renderAll()
}

// 重置视图
const resetView = () => {
  if (!fabricCanvas) return

  fabricCanvas.setZoom(1)
  fabricCanvas.viewportTransform = [1, 0, 0, 1, 0, 0]
  zoomLevel.value = 1
  emit('zoomChanged', zoomLevel.value)
  updateGrid()
  fabricCanvas.renderAll()
}

// 缩放控制
const zoomIn = () => {
  if (!fabricCanvas) return
  let zoom = fabricCanvas.getZoom() * 1.1
  if (zoom > maxZoom) zoom = maxZoom
  fabricCanvas.setZoom(zoom)
  zoomLevel.value = Math.round(zoom * 100) / 100
  emit('zoomChanged', zoomLevel.value)
  updateGrid()
}

const zoomOut = () => {
  if (!fabricCanvas) return
  let zoom = fabricCanvas.getZoom() / 1.1
  if (zoom < minZoom) zoom = minZoom
  fabricCanvas.setZoom(zoom)
  zoomLevel.value = Math.round(zoom * 100) / 100
  emit('zoomChanged', zoomLevel.value)
  updateGrid()
}

// 适应画布大小
const fitToCanvas = () => {
  if (!fabricCanvas || !currentFrame.value) return

  const canvasWidth = fabricCanvas.getWidth()
  const canvasHeight = fabricCanvas.getHeight()
  const imgWidth = currentFrame.value.width! * currentFrame.value.scaleX!
  const imgHeight = currentFrame.value.height! * currentFrame.value.scaleY!

  const scaleX = canvasWidth / imgWidth
  const scaleY = canvasHeight / imgHeight
  const scale = Math.min(scaleX, scaleY) * 0.9 // 留一点边距

  fabricCanvas.setZoom(scale)
  zoomLevel.value = Math.round(scale * 100) / 100
  emit('zoomChanged', zoomLevel.value)
  updateGrid()
  fabricCanvas.renderAll()
}

// 初始化画布
const initCanvas = async () => {
  if (!canvasRef.value || !canvasContainer.value) return

  try {
    // 等待容器尺寸就绪
    const { width, height } = await waitForContainerSize()

    // 创建Fabric.js画布实例
    fabricCanvas = new (fabric as any).Canvas(canvasRef.value, {
      width,
      height,
      backgroundColor: canvasConfig.backgroundColor,
      selection: canvasConfig.selection,
      preserveObjectStacking: canvasConfig.preserveObjectStacking
    })

    // 绘制网格
    drawGrid()

    // 添加事件监听
    addEventListeners()

    // 延迟更新画布尺寸
    setTimeout(() => {
      updateCanvasSize()
    }, 100)

    setTimeout(() => {
      updateCanvasSize()
    }, 500)

    canvasReady.value = true
    if (fabricCanvas) {
      emit('canvasReady', fabricCanvas)
    }

  } catch (error) {
    console.error('画布初始化失败:', error)
    emit('error', '画布初始化失败')
  }
}

// 监听属性变化
watch(() => props.showGrid, () => {
  drawGrid()
})

watch(() => props.scale, (newScale: number) => {
  if (fabricCanvas && newScale !== zoomLevel.value) {
    fabricCanvas.setZoom(newScale)
    zoomLevel.value = newScale
    updateGrid()
    fabricCanvas.renderAll()
  }
})

watch(() => props.position, (newPosition: { x: number, y: number }) => {
  if (fabricCanvas) {
    const vpt = fabricCanvas.viewportTransform!
    vpt[4] = newPosition.x
    vpt[5] = newPosition.y
    fabricCanvas.requestRenderAll()
  }
})

// 暴露方法给父组件
defineExpose({
  fabricCanvas: () => fabricCanvas,
  updateCanvasSize,
  updateGrid,
  zoomIn,
  zoomOut,
  resetView,
  fitToCanvas,
  clearCanvas,
  processFrameData,
  renderFrameOptimized,
  getPerformanceStats: () => performanceStats.value,
  getCurrentFrame: () => currentFrame.value,
  getFrameCount: () => frameCount.value,
  getFps: () => fps.value,
  getRenderQueueSize: () => renderQueue.value.length
})

// 组件挂载
onMounted(async () => {
  await nextTick()
  await initCanvas()
  window.addEventListener('resize', handleResize)
})

// 组件卸载
onUnmounted(() => {
  // 清理资源 - 释放ImageBitmap
  frameBuffer.value.forEach((imageBitmap: ImageBitmap) => imageBitmap.close())
  frameBuffer.value = []

  // 清理渲染队列
  renderQueue.value.forEach((imageBitmap: ImageBitmap) => imageBitmap.close())
  renderQueue.value = []

  if (fabricCanvas) {
    fabricCanvas.dispose()
    fabricCanvas = null
  }

  window.removeEventListener('resize', handleResize)
})
</script>

<template>
  <div
    ref="canvasContainer"
    class="video-stream-canvas"
    :class="{ 'loading': isLoading }"
  >
    <canvas ref="canvasRef"></canvas>
    
    <!-- 性能信息显示 -->
    <div v-if="canvasReady" class="performance-info">
      <span>FPS: {{ fps }}</span>
      <span>帧数: {{ frameCount }}</span>
    </div>

    <!-- 加载指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <el-icon class="is-loading">
        <Loading />
      </el-icon>
      <p>初始化画布中...</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.video-stream-canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #1a1a1a;
  width: 100%;
  height: 100%;

  &.loading {
    pointer-events: none;
  }

  canvas {
    border: 1px solid #404040;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    background: #1a1a1a;
    display: block;
  }

  .performance-info {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;

    span {
      margin-right: 12px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }

  .loading-indicator {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #ccc;

    .el-icon {
      font-size: 32px;
      margin-bottom: 8px;
    }
  }
}
</style>
