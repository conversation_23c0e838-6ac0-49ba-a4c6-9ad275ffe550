import { ref, computed, onUnmounted } from 'vue'

export interface FrameData {
  id: string
  timestamp: number
  blobUrl: string
  size: number
  format: string
  width?: number
  height?: number
}

export interface FrameManagerOptions {
  maxBufferSize?: number
  targetFps?: number
  enableFrameSkipping?: boolean
  memoryThreshold?: number // MB
  onFrameDropped?: (frame: FrameData) => void
  onMemoryWarning?: (usage: number) => void
}

export interface FrameStats {
  totalFrames: number
  bufferedFrames: number
  droppedFrames: number
  currentFps: number
  averageFps: number
  memoryUsage: number
  lastFrameTime: number
}

export function useFrameManager(options: FrameManagerOptions = {}) {
  // 配置选项
  const config = {
    maxBufferSize: options.maxBufferSize || 30,
    targetFps: options.targetFps || 30,
    enableFrameSkipping: options.enableFrameSkipping || true,
    memoryThreshold: options.memoryThreshold || 100 // 100MB
  }

  // 帧缓存
  const frameBuffer = ref<FrameData[]>([])
  const currentFrame = ref<FrameData | null>(null)

  // 性能统计
  const stats = ref<FrameStats>({
    totalFrames: 0,
    bufferedFrames: 0,
    droppedFrames: 0,
    currentFps: 0,
    averageFps: 0,
    memoryUsage: 0,
    lastFrameTime: 0
  })

  // FPS 计算相关
  const frameTimestamps = ref<number[]>([])
  const lastFrameTime = ref(0)
  const frameInterval = computed(() => 1000 / config.targetFps)

  // 内存使用监控
  const memoryUsage = ref(0)
  let memoryCheckTimer: NodeJS.Timeout | null = null

  // 添加新帧
  const addFrame = async (arrayBuffer: ArrayBuffer, format = 'jpeg'): Promise<boolean> => {
    const now = Date.now()

    // 帧率控制 - 如果启用了帧跳过且帧率过高
    if (config.enableFrameSkipping && 
        lastFrameTime.value > 0 && 
        (now - lastFrameTime.value) < frameInterval.value) {
      stats.value.droppedFrames++
      return false
    }

    try {
      // 创建帧数据
      const frameId = `frame_${now}_${Math.random().toString(36).substr(2, 9)}`
      const blobUrl = URL.createObjectURL(new Blob([arrayBuffer], { 
        type: `image/${format}` 
      }))

      const frameData: FrameData = {
        id: frameId,
        timestamp: now,
        blobUrl,
        size: arrayBuffer.byteLength,
        format
      }

      // 检查缓存大小，移除旧帧
      while (frameBuffer.value.length >= config.maxBufferSize) {
        const oldFrame = frameBuffer.value.shift()
        if (oldFrame) {
          URL.revokeObjectURL(oldFrame.blobUrl)
          options.onFrameDropped?.(oldFrame)
        }
      }

      // 添加新帧
      frameBuffer.value.push(frameData)
      currentFrame.value = frameData

      // 更新统计信息
      stats.value.totalFrames++
      stats.value.bufferedFrames = frameBuffer.value.length
      stats.value.lastFrameTime = now
      lastFrameTime.value = now

      // 更新 FPS 计算
      updateFpsStats(now)

      // 更新内存使用统计
      updateMemoryUsage()

      return true

    } catch (error) {
      console.error('添加帧失败:', error)
      return false
    }
  }

  // 获取当前帧
  const getCurrentFrame = (): FrameData | null => {
    return currentFrame.value
  }

  // 获取指定帧
  const getFrame = (frameId: string): FrameData | null => {
    return frameBuffer.value.find(frame => frame.id === frameId) || null
  }

  // 获取最近的 N 帧
  const getRecentFrames = (count: number): FrameData[] => {
    return frameBuffer.value.slice(-count)
  }

  // 清空缓存
  const clearBuffer = () => {
    frameBuffer.value.forEach(frame => {
      URL.revokeObjectURL(frame.blobUrl)
    })
    frameBuffer.value = []
    currentFrame.value = null
    stats.value.bufferedFrames = 0
    memoryUsage.value = 0
  }

  // 移除指定帧
  const removeFrame = (frameId: string): boolean => {
    const index = frameBuffer.value.findIndex(frame => frame.id === frameId)
    if (index !== -1) {
      const frame = frameBuffer.value[index]
      URL.revokeObjectURL(frame.blobUrl)
      frameBuffer.value.splice(index, 1)
      stats.value.bufferedFrames = frameBuffer.value.length
      updateMemoryUsage()
      return true
    }
    return false
  }

  // 更新 FPS 统计
  const updateFpsStats = (timestamp: number) => {
    frameTimestamps.value.push(timestamp)

    // 只保留最近 1 秒的时间戳
    const oneSecondAgo = timestamp - 1000
    frameTimestamps.value = frameTimestamps.value.filter(t => t > oneSecondAgo)

    // 计算当前 FPS
    stats.value.currentFps = frameTimestamps.value.length

    // 计算平均 FPS
    if (stats.value.totalFrames > 0) {
      const totalTime = timestamp - (frameTimestamps.value[0] || timestamp)
      stats.value.averageFps = totalTime > 0 
        ? Math.round((frameTimestamps.value.length * 1000) / totalTime)
        : 0
    }
  }

  // 更新内存使用统计
  const updateMemoryUsage = () => {
    const totalSize = frameBuffer.value.reduce((sum, frame) => sum + frame.size, 0)
    memoryUsage.value = totalSize / (1024 * 1024) // 转换为 MB
    stats.value.memoryUsage = memoryUsage.value

    // 内存警告
    if (memoryUsage.value > config.memoryThreshold) {
      options.onMemoryWarning?.(memoryUsage.value)
    }
  }

  // 自动内存清理
  const performMemoryCleanup = () => {
    if (memoryUsage.value > config.memoryThreshold) {
      // 移除一半的旧帧
      const framesToRemove = Math.floor(frameBuffer.value.length / 2)
      for (let i = 0; i < framesToRemove; i++) {
        const frame = frameBuffer.value.shift()
        if (frame) {
          URL.revokeObjectURL(frame.blobUrl)
          stats.value.droppedFrames++
        }
      }
      stats.value.bufferedFrames = frameBuffer.value.length
      updateMemoryUsage()
      console.log(`内存清理完成，移除了 ${framesToRemove} 帧`)
    }
  }

  // 获取缓存信息
  const getBufferInfo = () => ({
    totalFrames: frameBuffer.value.length,
    oldestFrame: frameBuffer.value[0] || null,
    newestFrame: frameBuffer.value[frameBuffer.value.length - 1] || null,
    memoryUsage: memoryUsage.value,
    bufferDuration: frameBuffer.value.length > 1 
      ? frameBuffer.value[frameBuffer.value.length - 1].timestamp - frameBuffer.value[0].timestamp
      : 0
  })

  // 设置目标 FPS
  const setTargetFps = (fps: number) => {
    config.targetFps = Math.max(1, Math.min(60, fps))
  }

  // 设置缓存大小
  const setBufferSize = (size: number) => {
    config.maxBufferSize = Math.max(1, size)
    
    // 如果当前缓存超过新的大小限制，移除多余的帧
    while (frameBuffer.value.length > config.maxBufferSize) {
      const frame = frameBuffer.value.shift()
      if (frame) {
        URL.revokeObjectURL(frame.blobUrl)
      }
    }
    stats.value.bufferedFrames = frameBuffer.value.length
  }

  // 启用/禁用帧跳过
  const setFrameSkipping = (enabled: boolean) => {
    config.enableFrameSkipping = enabled
  }

  // 获取统计信息
  const getStats = (): FrameStats => ({
    ...stats.value
  })

  // 重置统计信息
  const resetStats = () => {
    stats.value = {
      totalFrames: 0,
      bufferedFrames: frameBuffer.value.length,
      droppedFrames: 0,
      currentFps: 0,
      averageFps: 0,
      memoryUsage: memoryUsage.value,
      lastFrameTime: 0
    }
    frameTimestamps.value = []
  }

  // 启动内存监控
  const startMemoryMonitoring = () => {
    if (memoryCheckTimer) return

    memoryCheckTimer = setInterval(() => {
      updateMemoryUsage()
      
      // 自动清理内存
      if (memoryUsage.value > config.memoryThreshold * 0.8) {
        performMemoryCleanup()
      }
    }, 5000) // 每 5 秒检查一次
  }

  // 停止内存监控
  const stopMemoryMonitoring = () => {
    if (memoryCheckTimer) {
      clearInterval(memoryCheckTimer)
      memoryCheckTimer = null
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    clearBuffer()
    stopMemoryMonitoring()
  })

  // 自动启动内存监控
  startMemoryMonitoring()

  return {
    // 状态
    frameBuffer,
    currentFrame,
    stats,
    memoryUsage,

    // 方法
    addFrame,
    getCurrentFrame,
    getFrame,
    getRecentFrames,
    clearBuffer,
    removeFrame,
    getBufferInfo,
    setTargetFps,
    setBufferSize,
    setFrameSkipping,
    getStats,
    resetStats,
    performMemoryCleanup,
    startMemoryMonitoring,
    stopMemoryMonitoring
  }
}
